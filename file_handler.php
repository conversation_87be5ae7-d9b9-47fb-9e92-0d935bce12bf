<?php
// Start session only if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

require 'koneksi.php';

// Check if user is logged in
if (!isset($_SESSION['nama']) || !isset($_SESSION['level'])) {
    http_response_code(403);
    die('Access denied: Not logged in');
}

// Get file ID from request
$file_id = isset($_GET['id']) ? intval($_GET['id']) : 0;
$action = isset($_GET['action']) ? $_GET['action'] : 'view';

if ($file_id <= 0) {
    http_response_code(400);
    die('Invalid file ID');
}

// Get file information
$query = "SELECT fg.*, p.nama_petugas as uploader_name
          FROM file_gambar fg
          LEFT JOIN petugas p ON fg.uploaded_by = p.id_petugas
          WHERE fg.id = ?";

$stmt = mysqli_prepare($koneksi, $query);
mysqli_stmt_bind_param($stmt, "i", $file_id);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

if (!$result || mysqli_num_rows($result) == 0) {
    http_response_code(404);
    die('File not found');
}

$file_data = mysqli_fetch_assoc($result);
$file_path = "file_proyek/" . $file_data['gambar'];

// Check if file exists on disk
if (!file_exists($file_path)) {
    http_response_code(404);
    die('File not found on disk');
}

// Check access permissions
$user_level = $_SESSION['level'];
$user_id = $_SESSION['id_petugas'] ?? 0;

$has_access = false;

// Admin and project team always have access
if ($user_level == 'admin' || $user_level == 'proyek') {
    $has_access = true;
} 
// Client access control
elseif ($user_level == 'client') {
    // Client can access all files for review and approval purposes
    // This is necessary for the approval workflow
    $has_access = true;
}

if (!$has_access) {
    http_response_code(403);
    die('Access denied: Insufficient permissions');
}

// Log file access
$log_query = "INSERT INTO login_logs (user_id, email, ip_address, status, created_at)
              VALUES (?, ?, ?, 'success', NOW())";
$log_stmt = mysqli_prepare($koneksi, $log_query);
$user_email = $_SESSION['user'] ?? $_SESSION['nama'] ?? 'unknown';
$ip_address = $_SERVER['REMOTE_ADDR'];
mysqli_stmt_bind_param($log_stmt, "iss", $user_id, $user_email, $ip_address);
mysqli_stmt_execute($log_stmt);

// Handle different actions
if ($action == 'download') {
    // Force download
    header('Content-Type: application/octet-stream');
    header('Content-Disposition: attachment; filename="' . basename($file_data['gambar']) . '"');
    header('Content-Length: ' . filesize($file_path));
    header('Cache-Control: no-cache, must-revalidate');
    header('Pragma: no-cache');
    
    readfile($file_path);
    exit;
} 
elseif ($action == 'view') {
    // Determine content type based on file extension
    $file_ext = strtolower(pathinfo($file_data['gambar'], PATHINFO_EXTENSION));
    $content_type = 'application/octet-stream';
    
    switch ($file_ext) {
        case 'jpg':
        case 'jpeg':
            $content_type = 'image/jpeg';
            break;
        case 'png':
            $content_type = 'image/png';
            break;
        case 'gif':
            $content_type = 'image/gif';
            break;
        case 'pdf':
            $content_type = 'application/pdf';
            break;
        case 'dwg':
            $content_type = 'application/acad';
            break;
        default:
            $content_type = 'application/octet-stream';
    }
    
    header('Content-Type: ' . $content_type);
    header('Content-Length: ' . filesize($file_path));
    header('Cache-Control: public, max-age=3600');
    
    readfile($file_path);
    exit;
}
else {
    http_response_code(400);
    die('Invalid action');
}
?>

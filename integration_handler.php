<?php
require 'koneksi.php';

/**
 * Update task progress based on file approval status
 * This function is called when a file status changes
 */
function updateTaskProgressFromFileStatus($koneksi, $file_id) {
    // Since files are no longer linked to tasks, this function is deprecated
    // but kept for compatibility. It now just returns true.
    return true;
}

/**
 * Sync all task progress based on file approval status
 * This can be run periodically to ensure consistency
 */
function syncAllTaskProgress($koneksi) {
    // Since files are no longer linked to tasks, this function is deprecated
    // but kept for compatibility. It now just returns 0.
    return 0;
}

/**
 * Create verification entry when file is uploaded
 */
function createVerificationForFile($koneksi, $file_id) {
    // Check if verification already exists
    $check_query = "SELECT id FROM verifikasi WHERE file_id = ?";
    $check_stmt = mysqli_prepare($koneksi, $check_query);
    mysqli_stmt_bind_param($check_stmt, "i", $file_id);
    mysqli_stmt_execute($check_stmt);
    $check_result = mysqli_stmt_get_result($check_stmt);

    if (mysqli_num_rows($check_result) > 0) {
        return true; // Already exists
    }

    // Create verification entry for all uploaded files
    $insert_query = "INSERT INTO verifikasi (file_id, status_verifikasi, created_at)
                     VALUES (?, 'pending', NOW())";
    $insert_stmt = mysqli_prepare($koneksi, $insert_query);
    mysqli_stmt_bind_param($insert_stmt, "i", $file_id);

    return mysqli_stmt_execute($insert_stmt);
}

/**
 * Update verification status when file status changes
 */
function updateVerificationFromFileStatus($koneksi, $file_id) {
    // Get file status
    $file_query = "SELECT status, approved_by FROM file_gambar WHERE id = ?";
    $file_stmt = mysqli_prepare($koneksi, $file_query);
    mysqli_stmt_bind_param($file_stmt, "i", $file_id);
    mysqli_stmt_execute($file_stmt);
    $file_result = mysqli_stmt_get_result($file_stmt);
    
    if (!$file_result || mysqli_num_rows($file_result) == 0) {
        return false;
    }
    
    $file_data = mysqli_fetch_assoc($file_result);
    
    // Map file status to verification status
    $verification_status = 'pending';
    switch ($file_data['status']) {
        case 'approved':
            $verification_status = 'approved';
            break;
        case 'rejected':
            $verification_status = 'rejected';
            break;
        case 'revision':
            $verification_status = 'revision';
            break;
        default:
            $verification_status = 'pending';
    }
    
    // Update verification
    $update_query = "UPDATE verifikasi 
                     SET status_verifikasi = ?, 
                         verified_by = ?, 
                         verified_at = NOW() 
                     WHERE file_id = ?";
    $update_stmt = mysqli_prepare($koneksi, $update_query);
    mysqli_stmt_bind_param($update_stmt, "sii", $verification_status, $file_data['approved_by'], $file_id);
    
    return mysqli_stmt_execute($update_stmt);
}

// Handle AJAX calls for manual sync
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action'])) {
    // Start session only if not already started
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }
    
    if (!isset($_SESSION['nama']) || ($_SESSION['level'] != 'admin' && $_SESSION['level'] != 'proyek')) {
        http_response_code(403);
        die('Access denied');
    }
    
    $action = $_POST['action'];
    $response = ['success' => false, 'message' => 'Invalid action'];
    
    switch ($action) {
        case 'sync_progress':
            $updated_count = syncAllTaskProgress($koneksi);
            $response = [
                'success' => true, 
                'message' => "Berhasil sinkronisasi $updated_count tugas",
                'updated_count' => $updated_count
            ];
            break;
            
        case 'update_task_from_file':
            $file_id = intval($_POST['file_id'] ?? 0);
            if ($file_id > 0) {
                if (updateTaskProgressFromFileStatus($koneksi, $file_id)) {
                    $response = ['success' => true, 'message' => 'Progress tugas berhasil diupdate'];
                } else {
                    $response = ['success' => false, 'message' => 'Gagal mengupdate progress tugas'];
                }
            } else {
                $response = ['success' => false, 'message' => 'File ID tidak valid'];
            }
            break;
    }
    
    header('Content-Type: application/json');
    echo json_encode($response);
    exit;
}
?>
